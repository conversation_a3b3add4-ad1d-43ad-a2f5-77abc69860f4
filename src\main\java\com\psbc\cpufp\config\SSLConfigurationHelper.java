package com.psbc.cpufp.config;

import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * SSL配置辅助类，用于解决SSL握手失败问题
 */
public class SSLConfigurationHelper {
    
    private static final Logger log = LoggerFactory.getLogger(SSLConfigurationHelper.class);
    
    /**
     * 创建宽松的TLS客户端参数，用于解决SSL握手失败问题
     */
    public static TLSClientParameters createLenientTLSClientParameters() {
        TLSClientParameters tlsParams = new TLSClientParameters();
        
        try {
            // 设置TLS协议版本为1.2，这是最兼容的版本
            tlsParams.setSecureSocketProtocol("TLSv1.2");
            
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                        // 信任所有客户端证书
                    }
                    
                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                        // 信任所有服务器证书
                        log.debug("Trusting server certificate: {}", 
                            chain != null && chain.length > 0 ? chain[0].getSubjectDN() : "Unknown");
                    }
                    
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }
            };
            
            tlsParams.setTrustManagers(trustAllCerts);
            
            // 禁用主机名验证
            tlsParams.setDisableCNCheck(true);
            
            // 禁用默认的SSL验证
            tlsParams.setUseHttpsURLConnectionDefaultSslSocketFactory(false);
            tlsParams.setUseHttpsURLConnectionDefaultHostnameVerifier(false);
            
            // 设置加密套件（如果需要）
            // tlsParams.setCipherSuites(getSupportedCipherSuites());
            
            log.info("Created lenient TLS client parameters for SSL connection");
            
        } catch (Exception e) {
            log.error("Failed to create TLS client parameters", e);
            throw new RuntimeException("SSL configuration failed", e);
        }
        
        return tlsParams;
    }
    
    /**
     * 获取支持的加密套件列表
     */
    private static String[] getSupportedCipherSuites() {
        try {
            SSLContext context = SSLContext.getInstance("TLSv1.2");
            context.init(null, null, new SecureRandom());
            SSLSocketFactory factory = context.getSocketFactory();
            String[] supportedCipherSuites = factory.getSupportedCipherSuites();
            
            log.debug("Supported cipher suites: {}", (Object) supportedCipherSuites);
            return supportedCipherSuites;
            
        } catch (NoSuchAlgorithmException | KeyManagementException e) {
            log.warn("Failed to get supported cipher suites", e);
            return null;
        }
    }
    
    /**
     * 全局禁用SSL验证（仅用于开发测试环境）
     */
    public static void disableSSLVerificationGlobally() {
        try {
            // 创建信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            // 安装信任所有证书的TrustManager
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            
            // 创建信任所有主机名的HostnameVerifier
            HostnameVerifier allHostsValid = new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true;
                }
            };
            
            // 安装信任所有主机名的HostnameVerifier
            HttpsURLConnection.setDefaultHostnameVerifier(allHostsValid);
            
            log.warn("SSL verification has been disabled globally - USE ONLY FOR DEVELOPMENT/TESTING!");
            
        } catch (Exception e) {
            log.error("Failed to disable SSL verification globally", e);
        }
    }
}
