server:
  port: 8443
spring:
  application:
    name: branch-agent
  profiles:
    active: dev
  #监控暴露端点
management:
  endpoints:
    web:
      exposure:
        include: "prometheus,health"
#已启动的kernel的grpc注册地址，如127.0.0.1:20081
soma:
  registryUrl: soma-kernel.cpufp-plat:20081
  #本地内嵌服务启动端口，须保证该端口未被其他进程占用
  remotePort: 9944
  #执行器校验令牌
  #  execAccessToken: b79558f7390c45d8bec48aa03fabaaa7
  execAccessToken: ${soma_token}
  #执行器集群id
  #  execGroupId: EIDbfc5af50-005c-11ee-883c-fa163e7876be
  execGroupId: ${soma_id}
  #该配置项须保证与执行器集群的注册方式一致
  isAutoRegistry: 1
  #日志适配器路径(可选，如果不配置日志打印方式默认为System.out.println)
  loggerAdapter: com.psbc.cpufp.soma.logger.SomaLogger
  #执行调度任务的最大线程数(可选)
  #  remoteMaxThreadNum: 3
  #当前执行器的版本 要与创建的执行器版本一致
  execVersion: default
jasypt:
  encryptor:
    password: ${JASYPT_PASSWORD}

# 综合办公系统配置
zhbg:
  zhbgPostUrl: http://127.0.0.1:9091/
  url30001: api/admin/xzp/queryYxjfLsxdye
  url20006: api/admin/xzp/queryJgzhInfo
  reqSysNo: 350200001
  zhbgPublicKey: 0438b7e7107b34d58ace4edea7a3526ba43b10cb430b1e29fe652c8acae44534f967837760983a41ae3d96635623abd70187023a5f21a67c7af6966e06e728c7f3
  selfPublicKey: 04684f10ee3193bdb9194bdb5b6bb630a9b6ce9a22d34fdc36174f887839dfc2d378c804bd251a1a88a2660f310658212a3d72196c4ecfa597458cea46d78710fa
  privateKey: ALLBTAQC5X1EGl2gov0IZRdXy3Sv22jqvFTz2aQhSgqT

# 外联系统配置
xmysfzjjg:
  IP: 127.0.0.1
  PORT: 9702
  WG_PORT: 8888
  HTTP_PORT: 8445
  MESSAGE: <?xml version="1.0" encoding="utf-8"?><content><head><statecode>1</statecode><msg>交易成功</msg></head><body><table_account><row><instructionno>*************</instructionno><issuccess>1</issuccess></row></table_account></body></content>

# WebService客户端配置
webservice:
  client:
    url: https://127.0.0.1:8443/xmysfzjjg/wservices/IWebServiceService?wsdl