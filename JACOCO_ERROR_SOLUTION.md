# JaCoCo ClassNotFoundException 解决方案

## 错误描述

```
Exception in thread "main" java.lang.NoClassDefFoundError: org/jacoco/agent/rt/internal_4a7f17c/Offline
    at com.psbc.cpufp.BranchAgentApplication.$jacocoInit(BranchAgentApplication.java)
    at com.psbc.cpufp.BranchAgentApplication.main(BranchAgentApplication.java)
Caused by: java.lang.ClassNotFoundException: org.jacoco.agent.rt.internal_4a7f17c.Offline
```

## 问题原因

这个错误是由于JaCoCo代码覆盖率工具的配置问题导致的：

1. **IDE自动启用**: IntelliJ IDEA或Eclipse可能自动启用了JaCoCo代码覆盖率
2. **Maven配置**: 父POM或profile中可能配置了JaCoCo插件
3. **JVM参数**: 运行时JVM参数中包含了JaCoCo agent配置
4. **缺少依赖**: JaCoCo agent JAR文件未正确添加到classpath

## 解决方案

### 方案1: 禁用IDE中的代码覆盖率

#### IntelliJ IDEA
1. 打开 **Run/Debug Configurations**
2. 选择你的应用程序配置
3. 在 **VM options** 中移除任何包含 `javaagent` 和 `jacoco` 的参数
4. 确保 **Code Coverage** 选项卡中没有启用覆盖率收集

#### Eclipse
1. 右键项目 → **Properties**
2. 选择 **Java Build Path** → **Libraries**
3. 移除任何JaCoCo相关的库
4. 检查 **Run Configurations** 中的JVM参数

### 方案2: 使用Maven Profile跳过JaCoCo

在命令行中使用以下命令启动应用：

```bash
# 跳过所有测试和代码检查
mvn spring-boot:run -DskipTests -Dcheckstyle.skip=true -Dspotbugs.skip=true

# 或者使用特定的profile
mvn spring-boot:run -P!jacoco
```

### 方案3: 添加JaCoCo依赖（如果需要保留覆盖率功能）

在 `pom.xml` 中添加JaCoCo依赖：

```xml
<dependencies>
    <!-- JaCoCo Runtime -->
    <dependency>
        <groupId>org.jacoco</groupId>
        <artifactId>org.jacoco.agent</artifactId>
        <version>0.8.8</version>
        <classifier>runtime</classifier>
        <scope>test</scope>
    </dependency>
</dependencies>

<build>
    <plugins>
        <!-- JaCoCo Plugin -->
        <plugin>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
            <version>0.8.8</version>
            <executions>
                <execution>
                    <goals>
                        <goal>prepare-agent</goal>
                    </goals>
                </execution>
                <execution>
                    <id>report</id>
                    <phase>test</phase>
                    <goals>
                        <goal>report</goal>
                    </goals>
                </execution>
            </executions>
        </plugin>
    </plugins>
</build>
```

### 方案4: 清理编译缓存

```bash
# 清理Maven缓存
mvn clean

# 清理IDE缓存
# IntelliJ IDEA: File → Invalidate Caches and Restart
# Eclipse: Project → Clean → Clean all projects
```

### 方案5: 检查JVM启动参数

确保运行配置中没有以下JVM参数：

```
-javaagent:path/to/jacocoagent.jar
-Djacoco.agent.rt.jar=path/to/jacocoagent.jar
```

## 推荐解决步骤

1. **首先尝试方案1**: 在IDE中禁用代码覆盖率功能
2. **如果问题持续**: 使用方案2的Maven命令启动
3. **清理缓存**: 执行方案4的清理操作
4. **如果需要覆盖率**: 使用方案3正确配置JaCoCo

## 验证解决方案

成功解决后，应用启动时应该看到正常的Spring Boot启动日志，而不是JaCoCo相关的错误。

```
2024-01-01 12:00:00 INFO  - Starting BranchAgentApplication
2024-01-01 12:00:01 INFO  - Started BranchAgentApplication in 2.5 seconds
```

## 预防措施

1. **项目配置**: 在项目中明确配置是否需要代码覆盖率
2. **团队约定**: 统一团队的IDE配置和Maven profile使用
3. **文档说明**: 在README中说明如何正确启动应用
4. **CI/CD配置**: 在持续集成中正确配置JaCoCo插件