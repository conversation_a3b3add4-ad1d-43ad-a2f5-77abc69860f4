# 业务转发服务指南

## 概述

本项目新增了业务转发服务功能，通过8446端口提供HTTP转发服务，将业务请求转发到12358端口的目标服务。该功能模仿了现有的XzpController转发机制，为业务系统提供了统一的接口访问入口。

## 功能特性

- **端口转发**: 8446端口 → 12358端口
- **业务接口**: 支持list、list1、ordin、ordin1、ordin2等业务方法
- **健康检查**: 提供服务状态监控
- **错误处理**: 完善的异常处理和错误响应
- **日志记录**: 详细的请求和响应日志

## 架构设计

```
客户端请求 → HTTP服务(8446端口) → BusinessUniversalHandler → 目标服务(12358端口) → 返回响应
```

### 核心组件

1. **BusinessHttpForwardingService**: 业务HTTP转发服务，负责启动8446端口的HTTP服务器
2. **BusinessUniversalHandler**: 业务请求处理器，处理所有`/api/business`路径的请求
3. **BusinessController**: Spring Boot控制器，提供REST API接口
4. **BusinessServiceImpl**: 业务服务实现，处理具体的业务逻辑
5. **BusinessTestController**: 测试控制器，用于模拟12358端口的服务

## 配置说明

在`application.yml`中添加以下配置：

```yaml
# 业务转发服务配置
business:
  forwarding:
    enabled: true          # 是否启用转发服务
    port: 8446            # 转发服务监听端口
    target:
      host: 127.0.0.1     # 目标服务主机
      port: 12358         # 目标服务端口
  target:
    url: http://127.0.0.1:9702  # 业务目标URL
  timeout:
    connect: 30000      # 连接超时时间(毫秒)
    socket: 30000       # Socket超时时间(毫秒)
```

## API接口

### 业务接口

所有业务接口都通过POST方法调用，请求体为JSON格式。

#### 1. 列表查询
```http
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active"
  }
}
```

#### 2. 列表查询1
```http
POST http://127.0.0.1:8446/api/business/list1
Content-Type: application/json

{
  "type": "advanced",
  "filters": {
    "date": "2024-01-01"
  }
}
```

#### 3. 普通接口请求
```http
POST http://127.0.0.1:8446/api/business/ordin
Content-Type: application/json

{
  "action": "query",
  "params": {
    "id": "12345"
  }
}
```

#### 4. 普通接口请求1
```http
POST http://127.0.0.1:8446/api/business/ordin1
Content-Type: application/json

{
  "operation": "update",
  "data": {
    "field1": "value1"
  }
}
```

#### 5. 普通接口请求2
```http
POST http://127.0.0.1:8446/api/business/ordin2
Content-Type: application/json

{
  "command": "process",
  "parameters": {
    "mode": "batch"
  }
}
```

### 健康检查接口

#### 1. 业务服务健康检查
```http
GET http://127.0.0.1:8446/api/business/health
```

#### 2. 转发服务健康检查
```http
GET http://127.0.0.1:8446/health
```

## 响应格式

### 成功响应
```json
{
  "success": true,
  "method": "list",
  "timestamp": "2024-01-01 12:00:00",
  "data": {
    // 具体的业务数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "timestamp": "2024-01-01 12:00:00"
}
```

## 测试功能

项目提供了测试控制器`BusinessTestController`，可以模拟12358端口的服务响应，便于开发和测试。

### 测试接口

```http
# 测试list方法
POST http://127.0.0.1:8443/test/business/list

# 测试ordin方法
POST http://127.0.0.1:8443/test/business/ordin

# 通用测试接口
POST http://127.0.0.1:8443/test/business/{method}

# 测试健康检查
GET http://127.0.0.1:8443/test/business/health
```

## 启动和使用

1. **启动应用**: 启动Spring Boot应用，业务转发服务会自动启动
2. **验证服务**: 访问健康检查接口确认服务正常
3. **测试转发**: 通过8446端口调用业务接口
4. **查看日志**: 观察控制台日志了解请求转发情况

## 日志示例

```
2024-01-01 12:00:00 INFO  - 业务HTTP转发服务启动成功，端口：8446
2024-01-01 12:00:01 INFO  - 收到业务HTTP请求: POST /api/business/list
2024-01-01 12:00:01 INFO  - 业务转发路径映射: /api/business/list -> /api/business/list
2024-01-01 12:00:01 INFO  - 目标URL: http://127.0.0.1:12358/api/business/list
2024-01-01 12:00:01 INFO  - 业务转发响应: {"success":true,...}
```

## 故障排除

### 常见问题

1. **端口占用**: 确保8446端口未被其他程序占用
2. **目标服务不可达**: 检查12358端口的目标服务是否正常运行
3. **配置错误**: 验证application.yml中的配置是否正确
4. **网络问题**: 检查防火墙和网络连接

### 调试建议

1. 查看应用启动日志
2. 使用健康检查接口验证服务状态
3. 检查目标服务的可用性
4. 使用测试接口验证功能

## 与原版本对比

| 特性 | 原BusUtil | 新业务转发服务 |
|------|-----------|----------------|
| 架构 | 单体工具类 | 微服务架构 |
| 协议 | 自定义协议 | HTTP/JSON |
| 端口 | 直连9702 | 8446→12358转发 |
| 日志 | 简单输出 | 结构化日志 |
| 错误处理 | 基础异常 | 完善的错误响应 |
| 健康检查 | 无 | 多层次健康检查 |
| 配置管理 | 硬编码 | 外部化配置 |

## 注意事项

1. 确保目标服务(12358端口)正常运行
2. 监控转发服务的性能和稳定性
3. 定期检查日志文件大小
4. 根据实际需求调整超时配置
5. 在生产环境中禁用测试接口