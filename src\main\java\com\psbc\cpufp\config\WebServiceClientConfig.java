package com.psbc.cpufp.config;

import com.psbc.cpufp.service.ClientIWebService;
import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.apache.cxf.frontend.ClientProxy;
import org.apache.cxf.jaxws.JaxWsProxyFactoryBean;
import org.apache.cxf.transport.http.HTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class WebServiceClientConfig {

    private static final Logger log = LoggerFactory.getLogger(WebServiceClientConfig.class);

    @Value("${webservice.client.url}")
    private String serviceUrl;

    @Bean
    public ClientIWebService webServiceClient() {
        log.info("Creating WebService client for URL: {}", serviceUrl);

        JaxWsProxyFactoryBean factoryBean = new JaxWsProxyFactoryBean();
        factoryBean.setServiceClass(ClientIWebService.class);
        factoryBean.setAddress(serviceUrl);

        // 创建客户端
        ClientIWebService client = (ClientIWebService) factoryBean.create();

        // 配置 SSL（如果需要）
        if (serviceUrl != null && serviceUrl.startsWith("https://")) {
            log.info("Configuring SSL for HTTPS WebService client");
            configureSSL(client);
        }

        return client;
    }

    private void configureSSL(ClientIWebService client) {
        try {
            log.info("Configuring SSL parameters for WebService client");

            org.apache.cxf.endpoint.Client cl = ClientProxy.getClient(client);
            HTTPConduit http = (HTTPConduit) cl.getConduit();

            TLSClientParameters tlsParams = new TLSClientParameters();

            // 强制使用TLS 1.2，避免TLS 1.1导致的握手失败
            tlsParams.setSecureSocketProtocol("TLSv1.2");
            log.info("Set SSL protocol to TLSv1.2 to avoid handshake failures with older protocols");

            // 配置信任所有证书（仅用于开发测试）
            tlsParams.setTrustManagers(new javax.net.ssl.TrustManager[] {
                new javax.net.ssl.X509TrustManager() {
                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {}
                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                        log.debug("Server certificate validation - Auth type: {}", authType);
                    }
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() { return null; }
                }
            });

            // 禁用主机名验证
            tlsParams.setDisableCNCheck(true);

            // 禁用默认的SSL验证
            tlsParams.setUseHttpsURLConnectionDefaultSslSocketFactory(false);
            tlsParams.setUseHttpsURLConnectionDefaultHostnameVerifier(false);

            http.setTlsClientParameters(tlsParams);

            log.info("SSL configuration completed successfully with TLSv1.2");

        } catch (Exception e) {
            log.error("Failed to configure SSL for WebService client", e);
            throw new RuntimeException("SSL configuration failed", e);
        }
    }
}