package com.psbc.cpufp.config;

import org.apache.cxf.configuration.jsse.TLSClientParameters;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.Arrays;
import java.util.List;

/**
 * 自适应SSL配置类
 * 尝试兼容不同的TLS协议版本，包括TLSv1.1
 */
public class AdaptiveSSLConfig {
    
    private static final Logger log = LoggerFactory.getLogger(AdaptiveSSLConfig.class);
    
    // 协议优先级列表（从最安全到最不安全）
    private static final List<String> PROTOCOL_PRIORITY = Arrays.asList(
        "TLSv1.3",
        "TLSv1.2", 
        "TLSv1.1",  // 为了兼容性包含，但不推荐
        "TLSv1",
        "TLS"
    );
    
    /**
     * 创建自适应的TLS客户端参数
     * 会尝试多个协议版本以确保兼容性
     */
    public static TLSClientParameters createAdaptiveTLSParameters() {
        TLSClientParameters tlsParams = new TLSClientParameters();
        
        // 尝试设置最佳可用的协议版本
        String selectedProtocol = selectBestAvailableProtocol();
        tlsParams.setSecureSocketProtocol(selectedProtocol);
        
        log.info("Selected SSL protocol: {}", selectedProtocol);
        
        // 配置信任管理器
        configureTrustManager(tlsParams);
        
        // 配置其他SSL参数
        configureSSLParameters(tlsParams);
        
        return tlsParams;
    }
    
    /**
     * 选择最佳可用的协议版本
     */
    private static String selectBestAvailableProtocol() {
        for (String protocol : PROTOCOL_PRIORITY) {
            if (isProtocolSupported(protocol)) {
                log.info("Protocol {} is supported and will be used", protocol);
                return protocol;
            } else {
                log.debug("Protocol {} is not supported, trying next", protocol);
            }
        }
        
        // 如果所有协议都不支持，使用默认的TLS
        log.warn("No preferred protocols supported, falling back to default TLS");
        return "TLS";
    }
    
    /**
     * 检查协议是否被JVM支持
     */
    private static boolean isProtocolSupported(String protocol) {
        try {
            javax.net.ssl.SSLContext.getInstance(protocol);
            return true;
        } catch (Exception e) {
            log.debug("Protocol {} not supported: {}", protocol, e.getMessage());
            return false;
        }
    }
    
    /**
     * 配置信任管理器
     */
    private static void configureTrustManager(TLSClientParameters tlsParams) {
        // 创建宽松的信任管理器（仅用于开发测试）
        TrustManager[] trustManagers = new TrustManager[] {
            new X509TrustManager() {
                @Override
                public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    log.debug("Client certificate validation - Auth type: {}", authType);
                }
                
                @Override
                public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    log.debug("Server certificate validation - Auth type: {}", authType);
                    if (chain != null && chain.length > 0) {
                        log.debug("Server certificate subject: {}", chain[0].getSubjectDN());
                    }
                }
                
                @Override
                public X509Certificate[] getAcceptedIssuers() {
                    return new X509Certificate[0];
                }
            }
        };
        
        tlsParams.setTrustManagers(trustManagers);
    }
    
    /**
     * 配置其他SSL参数
     */
    private static void configureSSLParameters(TLSClientParameters tlsParams) {
        // 禁用主机名验证
        tlsParams.setDisableCNCheck(true);
        
        // 禁用默认的SSL验证
        tlsParams.setUseHttpsURLConnectionDefaultSslSocketFactory(false);
        tlsParams.setUseHttpsURLConnectionDefaultHostnameVerifier(false);
        
        log.debug("SSL parameters configured with relaxed validation");
    }
    
    /**
     * 创建兼容TLSv1.1的特殊配置
     * 注意：这仍然依赖于服务器是否支持TLSv1.1
     */
    public static TLSClientParameters createTLSv11CompatibleParameters() {
        TLSClientParameters tlsParams = new TLSClientParameters();
        
        log.warn("Creating TLSv1.1 compatible configuration - this may fail if server has disabled TLSv1.1");
        
        // 强制使用TLSv1.1
        tlsParams.setSecureSocketProtocol("TLSv1.1");
        
        // 配置信任管理器
        configureTrustManager(tlsParams);
        
        // 配置其他参数
        configureSSLParameters(tlsParams);
        
        // 设置更宽松的加密套件（如果需要）
        // tlsParams.setCipherSuites(getLegacyCipherSuites());
        
        return tlsParams;
    }
    
    /**
     * 获取兼容旧协议的加密套件
     */
    private static String[] getLegacyCipherSuites() {
        // 返回一些与TLSv1.1兼容的加密套件
        return new String[] {
            "TLS_RSA_WITH_AES_128_CBC_SHA",
            "TLS_RSA_WITH_AES_256_CBC_SHA",
            "TLS_RSA_WITH_3DES_EDE_CBC_SHA",
            "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA",
            "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"
        };
    }
    
    /**
     * 测试连接到指定URL以验证SSL配置
     */
    public static boolean testSSLConnection(String url, TLSClientParameters tlsParams) {
        try {
            // 这里可以添加实际的连接测试逻辑
            log.info("Testing SSL connection to: {}", url);
            // 实际实现会创建连接并测试握手
            return true;
        } catch (Exception e) {
            log.error("SSL connection test failed: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取系统支持的所有SSL协议
     */
    public static String[] getSupportedProtocols() {
        try {
            javax.net.ssl.SSLContext context = javax.net.ssl.SSLContext.getDefault();
            javax.net.ssl.SSLSocketFactory factory = context.getSocketFactory();
            
            // 创建一个临时socket来获取支持的协议
            try (javax.net.ssl.SSLSocket socket = (javax.net.ssl.SSLSocket) factory.createSocket()) {
                return socket.getSupportedProtocols();
            }
        } catch (Exception e) {
            log.error("Failed to get supported protocols: {}", e.getMessage());
            return new String[]{"TLS"};
        }
    }
    
    /**
     * 记录当前系统的SSL配置信息
     */
    public static void logSSLInfo() {
        log.info("=== SSL Configuration Information ===");
        
        String[] supportedProtocols = getSupportedProtocols();
        log.info("Supported protocols: {}", Arrays.toString(supportedProtocols));
        
        try {
            javax.net.ssl.SSLContext context = javax.net.ssl.SSLContext.getDefault();
            log.info("Default SSL context protocol: {}", context.getProtocol());
        } catch (Exception e) {
            log.warn("Could not get default SSL context: {}", e.getMessage());
        }
        
        // 记录系统属性
        String httpsProtocols = System.getProperty("https.protocols");
        String jdkTlsProtocols = System.getProperty("jdk.tls.client.protocols");
        
        log.info("System property https.protocols: {}", httpsProtocols);
        log.info("System property jdk.tls.client.protocols: {}", jdkTlsProtocols);
        
        log.info("=== End SSL Configuration Information ===");
    }
}
