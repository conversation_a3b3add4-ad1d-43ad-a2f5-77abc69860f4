@echo off
echo ========================================
echo 测试8445端口HTTP服务
echo ========================================
echo.

echo 1. 测试GET接口...
curl -X GET http://127.0.0.1:8445/xzp/testGet
echo.
echo.

echo 2. 测试POST接口 - test...
curl -X POST http://127.0.0.1:8445/xzp/test -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
echo.
echo.

echo 3. 测试POST接口 - queryYxjfLsxdye...
curl -X POST http://127.0.0.1:8445/xzp/queryYxjfLsxdye -H "Content-Type: application/json" -d "{\"testParam\":\"testValue\"}"
echo.
echo.

echo 4. 检查端口监听状态...
netstat -an | findstr :8445
echo.

echo ========================================
echo 测试完成
echo ========================================
pause