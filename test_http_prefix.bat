@echo off
echo ========================================
echo HTTP转发服务前缀配置测试脚本
echo ========================================
echo.
echo 测试场景1: 无前缀配置 (默认)
echo 访问地址: http://127.0.0.1:8445/httpapi/xzp/test
echo 实际转发到: https://127.0.0.1:8443/xmysfzjjg/xzp/test
curl -X POST http://127.0.0.1:8445/httpapi/xzp/test -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
echo.
echo.
echo 测试场景2: 配置前缀为 /api/v1
echo 需要在application.yml中设置: HTTP_REQUEST_PREFIX: "/api/v1"
echo 访问地址: http://127.0.0.1:8445/api/v1/httpapi/xzp/test
echo 实际转发到: https://127.0.0.1:8443/xmysfzjjg/xzp/test
echo curl -X POST http://127.0.0.1:8445/api/v1/httpapi/xzp/test -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
echo.
echo 测试场景3: 配置前缀为 /branch-agent
echo 需要在application.yml中设置: HTTP_REQUEST_PREFIX: "/branch-agent"
echo 访问地址: http://127.0.0.1:8445/branch-agent/httpapi/xzp/test
echo 实际转发到: https://127.0.0.1:8443/xmysfzjjg/xzp/test
echo curl -X POST http://127.0.0.1:8445/branch-agent/httpapi/xzp/test -H "Content-Type: application/json" -d "{\"test\":\"data\"}"
echo.
echo ========================================
echo 配置说明:
echo 1. 在 application.yml 中修改 xmysfzjjg.HTTP_REQUEST_PREFIX 配置
echo 2. 重启服务后生效
echo 3. 前缀会自动处理格式（添加/移除斜杠）
echo 4. 留空表示不使用前缀
echo ========================================
pause