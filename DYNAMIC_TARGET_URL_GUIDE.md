# 动态目标URL转发功能指南

## 概述

本功能允许在调用 `/api/business/list` 接口时，通过请求参数动态指定转发的目标URL，而不是使用固定的配置。

## 功能特性

- **动态转发**: 支持在POST请求中指定 `targetUrl` 参数
- **参数清理**: 自动从转发请求中移除 `targetUrl` 参数
- **向后兼容**: 如果未提供 `targetUrl`，则使用默认配置
- **仅限特定接口**: 目前仅对 `/api/business/list` 接口生效

## 使用方法

### 1. 使用动态targetUrl

```http
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active"
  },
  "targetUrl": "http://192.168.1.100:9702/api/custom/endpoint"
}
```

### 2. 使用默认配置（向后兼容）

```http
POST http://127.0.0.1:8446/api/business/list
Content-Type: application/json

{
  "pageNum": 1,
  "pageSize": 10,
  "queryParams": {
    "status": "active"
  }
}
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| targetUrl | String | 否 | 完整的转发目标URL，包含协议、主机、端口和路径 |
| 其他参数 | - | - | 业务相关参数，会原样转发给目标服务 |

## targetUrl格式要求

- **完整URL**: 必须包含协议（http://或https://）
- **主机和端口**: 如 `192.168.1.100:9702`
- **路径**: 如 `/api/custom/endpoint`
- **示例**: `http://192.168.1.100:9702/api/custom/endpoint`

## 处理流程

1. **接收请求**: 8446端口接收POST请求
2. **路径检查**: 验证是否为 `/api/business/list` 接口
3. **参数解析**: 从请求体中提取 `targetUrl` 参数
4. **URL替换**: 使用 `targetUrl` 替换默认的转发地址
5. **参数清理**: 从请求体中移除 `targetUrl` 参数
6. **请求转发**: 将清理后的请求转发到指定的目标服务
7. **响应返回**: 将目标服务的响应原样返回给客户端

## 日志示例

```
2024-01-01 12:00:01 INFO  - 收到业务HTTP请求: POST /api/business/list
2024-01-01 12:00:01 INFO  - 请求体: {"pageNum":1,"pageSize":10,"targetUrl":"http://192.168.1.100:9702/api/custom/endpoint"}
2024-01-01 12:00:01 INFO  - 使用请求参数中的targetUrl: http://192.168.1.100:9702/api/custom/endpoint
2024-01-01 12:00:01 INFO  - 移除targetUrl后的请求体: {"pageNum":1,"pageSize":10}
2024-01-01 12:00:01 INFO  - 业务转发路径映射: /api/business/list -> /api/admin/xzp/queryYxjfLsxdye
2024-01-01 12:00:01 INFO  - 目标URL: http://192.168.1.100:9702/api/custom/endpoint
```

## 错误处理

### 1. JSON解析失败

如果请求体不是有效的JSON格式，系统会记录警告日志并使用默认配置：

```
WARN - 解析请求体中的targetUrl失败，使用默认配置: Unexpected character...
```

### 2. 目标服务不可达

如果指定的targetUrl无法访问，会返回错误响应：

```json
{
  "success": false,
  "message": "请求处理异常: Connection refused"
}
```

## 安全注意事项

1. **URL验证**: 建议在生产环境中添加targetUrl的白名单验证
2. **访问控制**: 确保只有授权的客户端可以指定自定义targetUrl
3. **网络隔离**: 避免将内网服务暴露给外部客户端

## 测试用例

项目中包含了完整的测试用例，位于：
`src/test/java/com/psbc/cpufp/httpApplication/BusinessUniversalHandlerTest.java`

测试覆盖以下场景：
- 使用自定义targetUrl的请求
- 不包含targetUrl的请求（向后兼容）
- 非list接口的请求（不受影响）

## 扩展说明

目前此功能仅对 `/api/business/list` 接口生效。如需扩展到其他接口，可以修改 `BusinessUniversalHandler.java` 中的条件判断逻辑。