package com.psbc.cpufp.ssl;

import javax.net.ssl.*;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * TLS版本兼容性测试
 * 验证不同TLS版本对SSL握手的影响
 */
public class TLSVersionTest {
    
    private static final String TEST_URL = "https://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("TLS版本兼容性测试");
        System.out.println("========================================");
        System.out.println("目标URL: " + TEST_URL);
        System.out.println();
        
        TLSVersionTest test = new TLSVersionTest();
        
        // 测试不同的TLS版本
        String[] tlsVersions = {"TLSv1", "TLSv1.1", "TLSv1.2", "TLSv1.3"};
        
        for (String version : tlsVersions) {
            test.testTLSVersion(version);
            System.out.println();
        }
        
        // 测试强制使用TLSv1.1的场景
        test.testForceTLSv11();
        
        System.out.println("========================================");
        System.out.println("测试结论:");
        System.out.println("- TLSv1.1及以下版本可能导致SSL握手失败");
        System.out.println("- 建议使用TLSv1.2或TLSv1.3");
        System.out.println("- 服务器通常禁用旧版本TLS协议");
        System.out.println("========================================");
    }
    
    /**
     * 测试指定的TLS版本
     */
    public void testTLSVersion(String tlsVersion) {
        System.out.println("=== 测试 " + tlsVersion + " ===");
        
        try {
            // 创建指定版本的SSL上下文
            SSLContext sslContext = SSLContext.getInstance(tlsVersion);
            
            // 配置信任所有证书
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            // 创建HTTPS连接
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier((hostname, session) -> true);
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            
            // 尝试连接
            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long endTime = System.currentTimeMillis();
            
            System.out.println("✓ " + tlsVersion + " - 连接成功!");
            System.out.println("  响应码: " + responseCode);
            System.out.println("  连接时间: " + (endTime - startTime) + "ms");
            System.out.println("  实际协议: " + connection.getCipherSuite());
            
            connection.disconnect();
            
        } catch (NoSuchAlgorithmException e) {
            System.out.println("✗ " + tlsVersion + " - 不支持的协议: " + e.getMessage());
        } catch (KeyManagementException e) {
            System.out.println("✗ " + tlsVersion + " - 密钥管理错误: " + e.getMessage());
        } catch (SSLHandshakeException e) {
            System.out.println("✗ " + tlsVersion + " - SSL握手失败: " + e.getMessage());
            if (e.getMessage().contains("protocol_version")) {
                System.out.println("  → 这是典型的协议版本不匹配错误");
                System.out.println("  → 服务器不支持 " + tlsVersion + " 协议");
            } else if (e.getMessage().contains("handshake_failure")) {
                System.out.println("  → 握手失败，可能是协议不兼容");
            }
        } catch (Exception e) {
            System.out.println("✗ " + tlsVersion + " - 其他错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试强制使用TLSv1.1的场景（模拟旧客户端）
     */
    public void testForceTLSv11() {
        System.out.println("=== 模拟旧客户端强制使用TLSv1.1 ===");
        
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 创建限制协议版本的SSLSocketFactory
            connection.setSSLSocketFactory(new RestrictedSSLSocketFactory(factory, new String[]{"TLSv1.1"}));
            connection.setHostnameVerifier((hostname, session) -> true);
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ 强制TLSv1.1 - 意外成功，响应码: " + responseCode);
            
        } catch (SSLHandshakeException e) {
            System.out.println("✗ 强制TLSv1.1 - 预期的握手失败: " + e.getMessage());
            System.out.println("  → 这证明了服务器拒绝TLSv1.1连接");
            System.out.println("  → 客户端必须升级到TLSv1.2或更高版本");
        } catch (Exception e) {
            System.out.println("✗ 强制TLSv1.1 - 其他错误: " + e.getMessage());
        }
    }
    
    /**
     * 限制协议版本的SSLSocketFactory
     */
    private static class RestrictedSSLSocketFactory extends SSLSocketFactory {
        private final SSLSocketFactory delegate;
        private final String[] enabledProtocols;
        
        public RestrictedSSLSocketFactory(SSLSocketFactory delegate, String[] enabledProtocols) {
            this.delegate = delegate;
            this.enabledProtocols = enabledProtocols;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            System.out.println("  限制协议版本为: " + java.util.Arrays.toString(enabledProtocols));
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port, java.net.InetAddress localHost, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port, localHost, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress address, int port, java.net.InetAddress localAddress, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(address, port, localAddress, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.Socket s, String host, int port, boolean autoClose) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(s, host, port, autoClose);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }
        
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }
    }
}
