package com.psbc.cpufp.dto;

import lombok.Data;

/**
 * Netty服务响应VO类
 * 用于封装从8888端口Netty服务返回的响应
 */
@Data
public class NettyResponseVO {
    
    /**
     * 响应代码
     * 0: 成功
     * 其他: 错误代码
     */
    private String code;
    
    /**
     * 响应内容
     * 将返回信息以字符串形式放入body内
     */
    private String body;
    
    /**
     * 构造成功响应
     * @param body 响应内容
     * @return NettyResponseVO
     */
    public static NettyResponseVO success(String body) {
        NettyResponseVO response = new NettyResponseVO();
        response.setCode("0");
        response.setBody(body);
        return response;
    }
    
    /**
     * 构造错误响应
     * @param code 错误代码
     * @param body 错误信息
     * @return NettyResponseVO
     */
    public static NettyResponseVO error(String code, String body) {
        NettyResponseVO response = new NettyResponseVO();
        response.setCode(code);
        response.setBody(body);
        return response;
    }
    
    /**
     * 构造错误响应（默认错误代码为-1）
     * @param body 错误信息
     * @return NettyResponseVO
     */
    public static NettyResponseVO error(String body) {
        return error("-1", body);
    }
}