package com.psbc.cpufp.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.Socket;
import java.nio.charset.StandardCharsets;

/**
 * Netty客户端服务
 * 用于与8888端口的Netty服务进行通信
 */
@Slf4j
@Service
public class NettyClientService {
    
    @Value("${netty.server.host:127.0.0.1}")
    private String nettyHost;
    
    @Value("${netty.server.port:8888}")
    private int nettyPort;
    
    /**
     * 发送消息到Netty服务器并获取响应
     * @param message 要发送的XML消息
     * @return 服务器响应
     * @throws Exception 通信异常
     */
    public String sendMessage(String message) throws Exception {
        Socket socket = null;
        try {
            // 创建连接
            socket = new Socket(nettyHost, nettyPort);
            socket.setSoTimeout(30000); // 设置30秒超时
            socket.setKeepAlive(true);   // 保持连接活跃
            socket.setTcpNoDelay(true);  // 禁用Nagle算法，立即发送数据
            
            log.info("连接到Netty服务器: {}:{}", nettyHost, nettyPort);
            
            // 发送消息
            sendFormattedMessage(socket, message);
            log.info("消息发送完成，等待响应...");
            
            // 等待一小段时间确保服务器处理完成
            Thread.sleep(100);
            
            // 接收响应
            String response = receiveFormattedMessage(socket);
            log.info("收到Netty服务器响应: {}", response);
            
            return response;
            
        } catch (Exception e) {
            log.error("与Netty服务器通信失败: {}", e.getMessage(), e);
            throw e;
        } finally {
            closeConnection(socket);
        }
    }
    
    /**
     * 发送格式化的消息（添加6位长度前缀）
     */
    private void sendFormattedMessage(Socket socket, String message) throws IOException {
        byte[] messageBytes = message.getBytes(StandardCharsets.UTF_8);
        int messageLength = messageBytes.length;
        
        // 格式化长度字段为6位字符串
        String lengthStr = String.format("%06d", messageLength);
        byte[] lengthBytes = lengthStr.getBytes(StandardCharsets.UTF_8);
        
        log.info("发送消息到Netty服务器，长度: {}, 内容: {}", messageLength, message);
        
        OutputStream out = socket.getOutputStream();
        
        // 发送长度字段
        out.write(lengthBytes);
        out.flush();
        log.debug("长度字段发送完成: {}", lengthStr);
        
        // 发送消息体
        out.write(messageBytes);
        out.flush();
        log.debug("消息体发送完成");
        
        // 确保数据发送完成
        try {
            Thread.sleep(50); // 等待50ms确保数据发送完成
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 接收格式化的消息（解析6位长度前缀）
     */
    private String receiveFormattedMessage(Socket socket) throws IOException {
        InputStream in = socket.getInputStream();
        
        // 读取长度字段（6字节）
        byte[] lengthBytes = readExactBytes(in, 6, "长度字段");
        
        String lengthStr = new String(lengthBytes, StandardCharsets.UTF_8).trim();
        int messageLength;
        try {
            messageLength = Integer.parseInt(lengthStr);
            log.info("Netty服务器响应消息长度: {}", messageLength);
        } catch (NumberFormatException e) {
            throw new IOException("无法解析长度字段: " + lengthStr);
        }
        
        if (messageLength <= 0 || messageLength > 1024 * 1024) { // 限制最大1MB
            throw new IOException("消息长度异常: " + messageLength);
        }
        
        // 读取消息体
        byte[] messageBytes = readExactBytes(in, messageLength, "消息体");
        
        String result = new String(messageBytes, StandardCharsets.UTF_8);
        log.info("成功接收Netty服务器完整消息，长度: {}", result.length());
        return result;
    }
    
    /**
     * 精确读取指定字节数
     */
    private byte[] readExactBytes(InputStream in, int length, String description) throws IOException {
        byte[] buffer = new byte[length];
        int totalBytesRead = 0;
        
        while (totalBytesRead < length) {
            int bytesRead = in.read(buffer, totalBytesRead, length - totalBytesRead);
            if (bytesRead == -1) {
                throw new IOException("连接在读取" + description + "时关闭，已读取: " + totalBytesRead + "/" + length);
            }
            totalBytesRead += bytesRead;
        }
        
        log.debug("成功读取{}: {} 字节", description, length);
        return buffer;
    }
    
    /**
     * 优雅关闭连接
     */
    private void closeConnection(Socket socket) {
        if (socket != null && !socket.isClosed()) {
            try {
                // 优雅关闭：先关闭输出流
                if (!socket.isOutputShutdown()) {
                    socket.shutdownOutput();
                }
                
                // 等待一小段时间
                Thread.sleep(100);
                
                // 关闭输入流
                if (!socket.isInputShutdown()) {
                    socket.shutdownInput();
                }
                
                // 最后关闭socket
                socket.close();
                log.info("Netty连接已优雅关闭");
            } catch (Exception e) {
                log.warn("关闭Netty连接时发生错误: {}", e.getMessage());
                try {
                    socket.close();
                } catch (IOException ex) {
                    log.warn("强制关闭Netty连接失败: {}", ex.getMessage());
                }
            }
        }
    }
}