package com.psbc.cpufp.ssl;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * 简单的SSL测试工具，不依赖外部日志库
 * 用于快速测试SSL连接和协议兼容性
 */
public class SimpleSSLTest {
    
    private static final String TEST_URL = "https://localhost:8443/xmysfzjjg/wservices/IWebServiceService?wsdl";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("SSL/TLS协议兼容性测试");
        System.out.println("========================================");
        System.out.println("目标URL: " + TEST_URL);
        System.out.println();
        
        SimpleSSLTest test = new SimpleSSLTest();
        
        // 测试不同的TLS协议版本
        String[] protocols = {"TLS", "TLSv1.1", "TLSv1.2", "TLSv1.3"};
        
        for (String protocol : protocols) {
            test.testProtocol(protocol);
            System.out.println();
        }
        
        // 测试协议不一致场景
        test.testProtocolMismatch();
        
        System.out.println("========================================");
        System.out.println("测试完成");
        System.out.println("========================================");
    }
    
    /**
     * 测试指定的SSL/TLS协议版本
     */
    public void testProtocol(String protocol) {
        System.out.println("=== 测试协议: " + protocol + " ===");
        
        try {
            // 创建SSL上下文
            SSLContext sslContext = SSLContext.getInstance(protocol);
            
            // 配置信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { 
                        System.out.println("  服务器证书验证 - 认证类型: " + authType);
                        if (certs != null && certs.length > 0) {
                            System.out.println("  证书主题: " + certs[0].getSubjectDN());
                        }
                    }
                }
            };
            
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            // 创建连接
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 设置SSL配置
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    System.out.println("  主机名验证: " + hostname + " -> " + session.getPeerHost());
                    return true; // 信任所有主机名
                }
            });
            
            // 设置连接属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒超时
            connection.setReadTimeout(10000);
            
            // 尝试连接
            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long endTime = System.currentTimeMillis();
            
            System.out.println("✓ 协议 " + protocol + " - 连接成功!");
            System.out.println("  响应码: " + responseCode);
            System.out.println("  连接时间: " + (endTime - startTime) + "ms");
            System.out.println("  使用的加密套件: " + connection.getCipherSuite());
            
            // 读取少量响应内容
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line = reader.readLine();
                if (line != null) {
                    String preview = line.length() > 100 ? line.substring(0, 100) + "..." : line;
                    System.out.println("  响应内容预览: " + preview);
                }
            }
            
            connection.disconnect();
            
        } catch (NoSuchAlgorithmException e) {
            System.out.println("✗ 协议 " + protocol + " - 不支持的算法: " + e.getMessage());
        } catch (KeyManagementException e) {
            System.out.println("✗ 协议 " + protocol + " - 密钥管理错误: " + e.getMessage());
        } catch (SSLHandshakeException e) {
            System.out.println("✗ 协议 " + protocol + " - SSL握手失败: " + e.getMessage());
            System.out.println("  这可能表示协议不一致或服务器不支持此协议版本");
        } catch (Exception e) {
            System.out.println("✗ 协议 " + protocol + " - 其他错误: " + e.getMessage());
        }
    }
    
    /**
     * 测试协议不一致场景
     */
    public void testProtocolMismatch() {
        System.out.println("=== 测试协议不一致场景 ===");
        
        try {
            // 强制使用旧版本协议连接现代服务器
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 创建自定义的SSLSocketFactory，限制协议版本
            connection.setSSLSocketFactory(new RestrictedSSLSocketFactory(factory, new String[]{"TLSv1"}));
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ 协议不一致测试 - 意外成功，响应码: " + responseCode);
            
        } catch (SSLHandshakeException e) {
            System.out.println("✓ 协议不一致测试 - 预期的握手失败: " + e.getMessage());
            System.out.println("  这证明了服务器正确拒绝了不兼容的协议版本");
        } catch (Exception e) {
            System.out.println("✗ 协议不一致测试 - 其他错误: " + e.getMessage());
        }
    }
    
    /**
     * 自定义SSLSocketFactory，用于限制协议版本
     */
    private static class RestrictedSSLSocketFactory extends SSLSocketFactory {
        private final SSLSocketFactory delegate;
        private final String[] enabledProtocols;
        
        public RestrictedSSLSocketFactory(SSLSocketFactory delegate, String[] enabledProtocols) {
            this.delegate = delegate;
            this.enabledProtocols = enabledProtocols;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            System.out.println("  限制协议版本为: " + java.util.Arrays.toString(enabledProtocols));
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port, java.net.InetAddress localHost, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port, localHost, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress address, int port, java.net.InetAddress localAddress, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(address, port, localAddress, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.Socket s, String host, int port, boolean autoClose) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(s, host, port, autoClose);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }
        
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }
    }
}
