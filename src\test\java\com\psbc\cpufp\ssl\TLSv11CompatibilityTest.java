package com.psbc.cpufp.ssl;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * TLSv1.1兼容性测试工具
 * 尝试各种方法来兼容TLSv1.1协议
 */
public class TLSv11CompatibilityTest {
    
    private static final String TEST_URL = "https://188.9.30.65:8443/xmysfzjjg/wservices/IWebServiceService?wsdl";
    
    public static void main(String[] args) {
        System.out.println("========================================");
        System.out.println("TLSv1.1兼容性测试工具");
        System.out.println("========================================");
        System.out.println("目标URL: " + TEST_URL);
        System.out.println();
        
        TLSv11CompatibilityTest test = new TLSv11CompatibilityTest();
        
        // 1. 直接测试TLSv1.1
        test.testDirectTLSv11();
        
        // 2. 测试系统属性设置
        test.testWithSystemProperties();
        
        // 3. 测试自定义SSLContext
        test.testCustomSSLContext();
        
        // 4. 测试协议降级
        test.testProtocolFallback();
        
        // 5. 测试不同的加密套件
        test.testWithDifferentCipherSuites();
        
        System.out.println("========================================");
        System.out.println("重要说明:");
        System.out.println("如果所有测试都失败，说明服务器已完全禁用TLSv1.1");
        System.out.println("这是服务器端的安全策略，客户端无法绕过");
        System.out.println("建议联系服务器管理员确认TLS支持策略");
        System.out.println("========================================");
    }
    
    /**
     * 直接测试TLSv1.1连接
     */
    public void testDirectTLSv11() {
        System.out.println("=== 测试1: 直接TLSv1.1连接 ===");
        
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.1");
            
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier((hostname, session) -> true);
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ 直接TLSv1.1连接成功! 响应码: " + responseCode);
            
        } catch (Exception e) {
            System.out.println("✗ 直接TLSv1.1连接失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试通过系统属性启用TLSv1.1
     */
    public void testWithSystemProperties() {
        System.out.println("=== 测试2: 系统属性启用TLSv1.1 ===");
        
        // 保存原始系统属性
        String originalHttpsProtocols = System.getProperty("https.protocols");
        String originalJdkProtocols = System.getProperty("jdk.tls.client.protocols");
        
        try {
            // 设置系统属性包含TLSv1.1
            System.setProperty("https.protocols", "TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.disabledAlgorithms", "");
            
            System.out.println("设置系统属性: https.protocols=TLSv1.1,TLSv1.2,TLSv1.3");
            
            SSLContext sslContext = SSLContext.getInstance("TLSv1.1");
            sslContext.init(null, null, new SecureRandom());
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ 系统属性TLSv1.1连接成功! 响应码: " + responseCode);
            
        } catch (Exception e) {
            System.out.println("✗ 系统属性TLSv1.1连接失败: " + e.getMessage());
        } finally {
            // 恢复原始系统属性
            if (originalHttpsProtocols != null) {
                System.setProperty("https.protocols", originalHttpsProtocols);
            } else {
                System.clearProperty("https.protocols");
            }
            if (originalJdkProtocols != null) {
                System.setProperty("jdk.tls.client.protocols", originalJdkProtocols);
            } else {
                System.clearProperty("jdk.tls.client.protocols");
            }
        }
        System.out.println();
    }
    
    /**
     * 测试自定义SSLContext
     */
    public void testCustomSSLContext() {
        System.out.println("=== 测试3: 自定义SSLContext ===");
        
        try {
            // 创建支持多协议的SSLContext
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 使用自定义的SocketFactory强制TLSv1.1
            connection.setSSLSocketFactory(new ForceTLSv11SocketFactory(factory));
            connection.setHostnameVerifier((hostname, session) -> true);
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ 自定义SSLContext TLSv1.1连接成功! 响应码: " + responseCode);
            
        } catch (Exception e) {
            System.out.println("✗ 自定义SSLContext TLSv1.1连接失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 测试协议降级策略
     */
    public void testProtocolFallback() {
        System.out.println("=== 测试4: 协议降级策略 ===");
        
        String[] protocols = {"TLSv1.3", "TLSv1.2", "TLSv1.1", "TLSv1"};
        
        for (String protocol : protocols) {
            try {
                System.out.println("尝试协议: " + protocol);
                
                SSLContext sslContext = SSLContext.getInstance(protocol);
                sslContext.init(null, null, new SecureRandom());
                
                URL url = new URL(TEST_URL);
                HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
                connection.setSSLSocketFactory(sslContext.getSocketFactory());
                connection.setHostnameVerifier((hostname, session) -> true);
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(5000);
                
                int responseCode = connection.getResponseCode();
                System.out.println("✓ 协议 " + protocol + " 连接成功! 响应码: " + responseCode);
                System.out.println("  → 建议使用此协议版本");
                break; // 成功后停止尝试
                
            } catch (Exception e) {
                System.out.println("✗ 协议 " + protocol + " 失败: " + e.getMessage());
            }
        }
        System.out.println();
    }
    
    /**
     * 测试不同的加密套件
     */
    public void testWithDifferentCipherSuites() {
        System.out.println("=== 测试5: TLSv1.1 + 兼容加密套件 ===");
        
        try {
            SSLContext sslContext = SSLContext.getInstance("TLSv1.1");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 使用兼容TLSv1.1的加密套件
            connection.setSSLSocketFactory(new TLSv11CipherSuiteFactory(factory));
            connection.setHostnameVerifier((hostname, session) -> true);
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000);
            
            int responseCode = connection.getResponseCode();
            System.out.println("✓ TLSv1.1 + 兼容加密套件连接成功! 响应码: " + responseCode);
            
        } catch (Exception e) {
            System.out.println("✗ TLSv1.1 + 兼容加密套件连接失败: " + e.getMessage());
        }
        System.out.println();
    }
    
    /**
     * 强制使用TLSv1.1的SSLSocketFactory
     */
    private static class ForceTLSv11SocketFactory extends SSLSocketFactory {
        private final SSLSocketFactory delegate;
        
        public ForceTLSv11SocketFactory(SSLSocketFactory delegate) {
            this.delegate = delegate;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            System.out.println("  强制启用协议: TLSv1.1");
            return socket;
        }
        
        // 其他createSocket方法的实现...
        @Override
        public SSLSocket createSocket(String host, int port, java.net.InetAddress localHost, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port, localHost, localPort);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress address, int port, java.net.InetAddress localAddress, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(address, port, localAddress, localPort);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.Socket s, String host, int port, boolean autoClose) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(s, host, port, autoClose);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            return socket;
        }
        
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }
        
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }
    }
    
    /**
     * 使用TLSv1.1兼容加密套件的SSLSocketFactory
     */
    private static class TLSv11CipherSuiteFactory extends SSLSocketFactory {
        private final SSLSocketFactory delegate;
        
        // TLSv1.1兼容的加密套件
        private static final String[] TLSv11_CIPHER_SUITES = {
            "TLS_RSA_WITH_AES_128_CBC_SHA",
            "TLS_RSA_WITH_AES_256_CBC_SHA",
            "TLS_RSA_WITH_3DES_EDE_CBC_SHA",
            "TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA",
            "TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA"
        };
        
        public TLSv11CipherSuiteFactory(SSLSocketFactory delegate) {
            this.delegate = delegate;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            socket.setEnabledCipherSuites(TLSv11_CIPHER_SUITES);
            System.out.println("  设置TLSv1.1兼容加密套件");
            return socket;
        }
        
        // 其他方法实现类似...
        @Override
        public SSLSocket createSocket(String host, int port, java.net.InetAddress localHost, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port, localHost, localPort);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            socket.setEnabledCipherSuites(TLSv11_CIPHER_SUITES);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress host, int port) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            socket.setEnabledCipherSuites(TLSv11_CIPHER_SUITES);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress address, int port, java.net.InetAddress localAddress, int localPort) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(address, port, localAddress, localPort);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            socket.setEnabledCipherSuites(TLSv11_CIPHER_SUITES);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.Socket s, String host, int port, boolean autoClose) throws java.io.IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(s, host, port, autoClose);
            socket.setEnabledProtocols(new String[]{"TLSv1.1"});
            socket.setEnabledCipherSuites(TLSv11_CIPHER_SUITES);
            return socket;
        }
        
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }
        
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }
    }
}
