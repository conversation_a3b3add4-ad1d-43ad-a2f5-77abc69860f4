# SSL配置指南

## 概述
本文档说明了如何在branch-agent项目中启用和配置SSL/HTTPS功能。

## SSL配置详情

### 1. 配置文件修改
在 `src/main/resources/local/application.yml` 中启用了SSL配置：

```yaml
server:
  ssl:
    # 开启HTTPS访问
    enabled: true
    # 使用certServer.jks证书文件
    key-store: classpath:certServer.jks
    key-store-type: jks
    key-alias: certServer
    key-password: Psbc@xm2025
    key-store-password: Psbc@xm2025
    # SSL协议配置
    protocol: TLS
    # 启用的SSL协议版本
    enabled-protocols: TLSv1.2,TLSv1.3
```

### 2. 证书文件
项目使用的SSL证书文件：
- **主要证书**: `certServer.jks` (位于 `src/main/resources/`)
- **备用证书**: `server.keystore`, `xzp_zjjg.keystore` 等

### 3. 代码修改

#### HttpForwardingService.java
- 将Spring Boot基础URL从 `http://` 改为 `https://`
- 确保HTTP转发服务能正确转发到HTTPS的Spring Boot应用

#### XzpUniversalHandler.java
- 添加SSL配置，忽略证书验证以避免连接问题
- 配置TLS协议版本为TLSv1.2
- 使用 `disableDefaultSSLVerification()` 方法

### 4. 测试脚本更新
`test_http_prefix.bat` 脚本已更新：
- 实际转发地址从 `http://` 改为 `https://`
- 保持外部访问地址为 `http://`（HTTP转发服务仍使用HTTP）

## 访问方式变化

### 启用SSL前
- Spring Boot应用: `http://127.0.0.1:8443/xmysfzjjg/`
- HTTP转发服务: `http://127.0.0.1:8445/`

### 启用SSL后
- Spring Boot应用: `https://127.0.0.1:8443/xmysfzjjg/` ✅
- HTTP转发服务: `http://127.0.0.1:8445/` (仍为HTTP，内部转发到HTTPS)

## 安全注意事项

### 1. 证书管理
- 定期更新SSL证书
- 确保证书密码安全存储
- 生产环境使用正式CA签发的证书

### 2. 协议版本
- 启用了TLSv1.2和TLSv1.3
- 禁用了不安全的SSL/TLS版本

### 3. 内部通信
- HTTP转发服务到Spring Boot应用的通信使用HTTPS
- 配置了SSL证书验证忽略（仅限内部通信）

## 故障排除

### 常见问题

1. **证书文件找不到**
   - 确认 `certServer.jks` 文件存在于 `src/main/resources/` 目录
   - 检查文件路径和权限

2. **SSL握手失败**
   - 检查证书密码是否正确
   - 确认TLS协议版本兼容性

3. **Hutool SSL方法不存在错误**
   - 当前Hutool版本不支持高级SSL配置方法
   - 已移除不兼容的SSL配置，使用基础HTTP功能
   - 详见 `SSL_COMPATIBILITY_NOTES.md` 文档

4. **HTTP转发服务连接失败**
   - 确认目标HTTPS服务可访问
   - 检查SSL证书是否有效

### 日志检查
启动应用后，查看日志中的SSL相关信息：
```
INFO - Spring Boot应用基础URL: https://127.0.0.1:8443/xmysfzjjg
INFO - HTTP转发服务启动成功，端口：8445
```

## 配置验证

### 1. 直接访问Spring Boot应用
```bash
curl -k https://127.0.0.1:8443/xmysfzjjg/xzp/test
```

### 2. 通过HTTP转发服务访问
```bash
curl http://127.0.0.1:8445/xzp/test
```

### 3. 使用测试脚本
```bash
test_http_prefix.bat
```

## 生产环境建议

1. **使用正式证书**: 替换自签名证书为CA签发的正式证书
2. **启用证书验证**: 在生产环境中启用完整的SSL证书验证
3. **配置防火墙**: 适当配置防火墙规则
4. **监控SSL证书**: 设置证书过期提醒
5. **定期安全审计**: 定期检查SSL配置和证书状态

## 相关文件

- `src/main/resources/local/application.yml` - SSL配置
- `src/main/resources/certServer.jks` - SSL证书文件
- `HttpForwardingService.java` - HTTP转发服务
- `XzpUniversalHandler.java` - 请求处理器
- `test_http_prefix.bat` - 测试脚本
- `HTTP_PREFIX_TROUBLESHOOTING.md` - 前缀配置故障排除