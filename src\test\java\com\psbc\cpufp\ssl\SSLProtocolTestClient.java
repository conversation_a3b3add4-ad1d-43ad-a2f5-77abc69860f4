package com.psbc.cpufp.ssl;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;

/**
 * SSL/TLS协议测试客户端
 * 用于模拟不同的SSL/TLS协议版本请求，测试协议不一致问题
 */
public class SSLProtocolTestClient {
    
    private static final Logger log = LoggerFactory.getLogger(SSLProtocolTestClient.class);
    
    // 测试目标URL
    private static final String TEST_URL = "https://188.9.30.65:8443/xmysfzjjg/wservices/IWebServiceService?wsdl";
    
    public static void main(String[] args) {
        SSLProtocolTestClient client = new SSLProtocolTestClient();
        
        log.info("开始SSL/TLS协议兼容性测试...");
        log.info("目标URL: {}", TEST_URL);
        
        // 测试不同的SSL/TLS协议版本
        String[] protocols = {
            "SSL",          // 旧版SSL（应该失败）
            "SSLv2",        // SSL 2.0（应该失败）
            "SSLv3",        // SSL 3.0（应该失败）
            "TLS",          // 默认TLS
            "TLSv1",        // TLS 1.0
            "TLSv1.1",      // TLS 1.1
            "TLSv1.2",      // TLS 1.2（推荐）
            "TLSv1.3"       // TLS 1.3（最新）
        };
        
        for (String protocol : protocols) {
            client.testProtocol(protocol);
            System.out.println(); // 空行分隔
        }
        
        // 测试协议不一致场景
        client.testProtocolMismatch();
        
        // 测试证书验证场景
        client.testCertificateValidation();
    }
    
    /**
     * 测试指定的SSL/TLS协议版本
     */
    public void testProtocol(String protocol) {
        log.info("=== 测试协议: {} ===", protocol);
        
        try {
            // 创建SSL上下文
            SSLContext sslContext = SSLContext.getInstance(protocol);
            
            // 配置信任所有证书的TrustManager
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };
            
            sslContext.init(null, trustAllCerts, new SecureRandom());
            
            // 创建连接
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 设置SSL配置
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            connection.setHostnameVerifier(new HostnameVerifier() {
                public boolean verify(String hostname, SSLSession session) {
                    return true; // 信任所有主机名
                }
            });
            
            // 设置连接属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒超时
            connection.setReadTimeout(10000);
            
            // 尝试连接
            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long endTime = System.currentTimeMillis();
            
            log.info("协议 {} - 连接成功!", protocol);
            log.info("响应码: {}", responseCode);
            log.info("连接时间: {}ms", (endTime - startTime));
            log.info("使用的协议: {}", connection.getCipherSuite());
            
            // 读取少量响应内容
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()))) {
                String line = reader.readLine();
                if (line != null) {
                    log.info("响应内容预览: {}", line.substring(0, Math.min(100, line.length())));
                }
            }
            
            connection.disconnect();
            
        } catch (NoSuchAlgorithmException e) {
            log.error("协议 {} - 不支持的算法: {}", protocol, e.getMessage());
        } catch (KeyManagementException e) {
            log.error("协议 {} - 密钥管理错误: {}", protocol, e.getMessage());
        } catch (SSLHandshakeException e) {
            log.error("协议 {} - SSL握手失败: {}", protocol, e.getMessage());
        } catch (IOException e) {
            log.error("协议 {} - IO错误: {}", protocol, e.getMessage());
        } catch (Exception e) {
            log.error("协议 {} - 其他错误: {}", protocol, e.getMessage());
        }
    }
    
    /**
     * 测试协议不一致场景
     */
    public void testProtocolMismatch() {
        log.info("=== 测试协议不一致场景 ===");
        
        try {
            // 强制使用旧版本协议连接现代服务器
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom());
            
            SSLSocketFactory factory = sslContext.getSocketFactory();
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            // 创建自定义的SSLSocketFactory，限制协议版本
            connection.setSSLSocketFactory(new RestrictedSSLSocketFactory(factory, new String[]{"TLSv1"}));
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            log.info("协议不一致测试 - 意外成功，响应码: {}", responseCode);
            
        } catch (SSLHandshakeException e) {
            log.info("协议不一致测试 - 预期的握手失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("协议不一致测试 - 其他错误: {}", e.getMessage());
        }
    }
    
    /**
     * 测试证书验证场景
     */
    public void testCertificateValidation() {
        log.info("=== 测试证书验证场景 ===");
        
        try {
            // 使用默认的证书验证（严格模式）
            SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
            sslContext.init(null, null, new SecureRandom()); // 使用默认TrustManager
            
            URL url = new URL(TEST_URL);
            HttpsURLConnection connection = (HttpsURLConnection) url.openConnection();
            
            connection.setSSLSocketFactory(sslContext.getSocketFactory());
            // 不设置自定义HostnameVerifier，使用默认的严格验证
            
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);
            
            int responseCode = connection.getResponseCode();
            log.info("严格证书验证 - 成功，响应码: {}", responseCode);
            
        } catch (SSLHandshakeException e) {
            log.info("严格证书验证 - 预期的证书验证失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("严格证书验证 - 其他错误: {}", e.getMessage());
        }
    }
    
    /**
     * 自定义SSLSocketFactory，用于限制协议版本
     */
    private static class RestrictedSSLSocketFactory extends SSLSocketFactory {
        private final SSLSocketFactory delegate;
        private final String[] enabledProtocols;
        
        public RestrictedSSLSocketFactory(SSLSocketFactory delegate, String[] enabledProtocols) {
            this.delegate = delegate;
            this.enabledProtocols = enabledProtocols;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port) throws IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(String host, int port, java.net.InetAddress localHost, int localPort) throws IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port, localHost, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress host, int port) throws IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(host, port);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.InetAddress address, int port, java.net.InetAddress localAddress, int localPort) throws IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(address, port, localAddress, localPort);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public SSLSocket createSocket(java.net.Socket s, String host, int port, boolean autoClose) throws IOException {
            SSLSocket socket = (SSLSocket) delegate.createSocket(s, host, port, autoClose);
            socket.setEnabledProtocols(enabledProtocols);
            return socket;
        }
        
        @Override
        public String[] getDefaultCipherSuites() {
            return delegate.getDefaultCipherSuites();
        }
        
        @Override
        public String[] getSupportedCipherSuites() {
            return delegate.getSupportedCipherSuites();
        }
    }
}
