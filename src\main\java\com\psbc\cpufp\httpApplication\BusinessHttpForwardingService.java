package com.psbc.cpufp.httpApplication;

import com.sun.net.httpserver.HttpServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

/**
 * 业务HTTP转发服务
 * 负责启动独立的HTTP服务器，监听8446端口，转发业务请求到12358端口
 */
@Slf4j
@Component
public class BusinessHttpForwardingService {
    
    @Value("${business.forwarding.enabled:true}")
    private boolean forwardingEnabled;
    
    @Value("${business.forwarding.port:8446}")
    private int forwardingPort;
    
    @Value("${business.forwarding.target.host:127.0.0.1}")
    private String targetHost;
    
    @Value("${business.forwarding.target.port:12358}")
    private int targetPort;
    
    @Autowired
    private ApplicationContext applicationContext;
    
    private HttpServer httpServer;
    
    @PostConstruct
    public void startBusinessHttpService() {
        if (!forwardingEnabled) {
            log.info("业务HTTP转发服务已禁用，跳过启动");
            return;
        }
        
        try {
            // 构建目标服务的基础URL
            String targetBaseUrl = "http://" + targetHost + ":" + targetPort;
            
            log.info("业务HTTP转发服务配置:");
            log.info("  - 监听端口: {}", forwardingPort);
            log.info("  - 目标服务: {}", targetBaseUrl);
            
            httpServer = HttpServer.create(new InetSocketAddress(forwardingPort), 0);
            
            // 添加业务API处理器，处理所有/api/business路径的请求
            httpServer.createContext("/api/xzp/spf/20002", new BusinessUniversalHandler(targetBaseUrl, applicationContext));
            
            // 添加Netty代理处理器，处理转发到8888端口Netty服务的请求
            httpServer.createContext("/api/xzp/spf/20006", new NettyProxyHandler(applicationContext));
            
            // 添加健康检查接口
            httpServer.createContext("/health", exchange -> {
                String response = "{\"status\":\"UP\",\"service\":\"business-forwarding\",\"port\":" + forwardingPort + ",\"target\":\"" + targetBaseUrl + "\"}";
                exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
                byte[] responseBytes = response.getBytes("UTF-8");
                exchange.sendResponseHeaders(200, responseBytes.length);
                exchange.getResponseBody().write(responseBytes);
                exchange.getResponseBody().close();
            });
            
            httpServer.setExecutor(Executors.newFixedThreadPool(10));
            httpServer.start();
            
            log.info("业务HTTP转发服务启动成功，端口：{}", forwardingPort);
            log.info("业务转发服务已启动，可通过以下地址访问:");
            log.info("  - http://127.0.0.1:{}/api/xzp/spf/20002 (POST)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/business/list1 (POST)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/business/ordin (POST)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/business/ordin1 (POST)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/business/ordin2 (POST)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/business/health (GET)", forwardingPort);
            log.info("  - http://127.0.0.1:{}/api/netty/send (POST) - Netty代理接口", forwardingPort);
            log.info("  - http://127.0.0.1:{}/health (GET) - 转发服务健康检查", forwardingPort);
            
        } catch (Exception e) {
            log.error("业务HTTP转发服务启动失败: {}", e.getMessage(), e);
        }
    }
    
    @PreDestroy
    public void stopBusinessHttpService() {
        if (httpServer != null) {
            try {
                httpServer.stop(0);
                log.info("业务HTTP转发服务已关闭");
            } catch (Exception e) {
                log.error("关闭业务HTTP转发服务时发生错误: {}", e.getMessage(), e);
            }
        }
    }
}