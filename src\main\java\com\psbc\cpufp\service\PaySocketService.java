package com.psbc.cpufp.service;

import cn.hutool.http.HttpException;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.psbc.cpufp.config.ZhbgProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Service;

import javax.net.ssl.HttpsURLConnection;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;

@Slf4j
@Service
@RequiredArgsConstructor
public class PaySocketService {

    private final ZhbgProperties zhbgProperties;
    private final ClientIWebService webServiceClient;

    /**
     * 处理Socket消息的主要方法
     * @param message 接收到的消息
     * @return 处理后的响应消息
     */
    public String processMessage(String message) {
        try {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>接收银行端报文开始>>>>>>>>>>>>>>>>>>>>>>>>>>");
            log.info("银行端发送报文:[{}]", message);

            // 解析服务号
            String serviceNo = parseServiceNo(message);
            log.info("解析到的服务号: {}", serviceNo);

            // 根据服务号处理不同的业务逻辑
            if ("30001".equals(serviceNo)) {
                return handleZhbgService(message, serviceNo);
            }
//            else if ("20006".equals(serviceNo)) {
//                return handleZhbgService(message, serviceNo);
//            }
            else {
                // 原来的逻辑，发往委托方
                return handleOutsysService(message);
            }

        } catch (Exception e) {
            log.error("处理消息时发生错误", e);
            return "处理消息时发生错误: " + e.getMessage();
        }
    }

    /**
     * 解析消息中的服务号
     * @param message 消息内容
     * @return 服务号
     */
    private String parseServiceNo(String message) {
        try {
            // 判断是json还是xml
            if (isJson(message)) {
                log.info("JSON请求获取serviceno-----");
                HashMap<String, Object> map = JSON.parseObject(message, HashMap.class);
                Object serviceNo = map.get("serviceno");
                return serviceNo != null ? serviceNo.toString() : "";
            } else if (isXML(message)) {
                log.info("XML请求获取serviceno-----");
                Document doc = DocumentHelper.parseText(message);
                Element root = doc.getRootElement();
                log.info("根节点名称--{}", root.getName());
                
                // 获取根节点下的子节点head
                Iterator<Element> it = root.elementIterator("head");
                while (it.hasNext()) {
                    Element recordEle = it.next();
                    String serviceno = recordEle.elementTextTrim("serviceno");
                    log.info("接口编码--{}", serviceno);
                    return serviceno != null ? serviceno : "";
                }
            }
        } catch (Exception e) {
            log.error("解析服务号时发生错误", e);
        }
        return "";
    }

    /**
     * 处理综合办公服务
     * @param message 消息内容
     * @param serviceNo 服务号
     * @return 响应消息
     */
    private String handleZhbgService(String message, String serviceNo) {
        try {
            long start = System.nanoTime();
            String response = sendZhbg(message, serviceNo);
            long end = System.nanoTime();
            
            log.info("报文发往综合办公系统结束>>>");
            log.info("综合办公返回报文耗时:[{}]毫秒", (end - start) / 1000L / 1000L);
            log.info("综合办公返回报文长度:[{}]", response.getBytes("UTF-8").length);
            log.info("综合办公系统返回报文:[{}]", response);
            
            return response;
        } catch (Exception e) {
            log.error("处理综合办公服务时发生错误", e);
            return "处理综合办公服务时发生错误: " + e.getMessage();
        }
    }

    /**
     * 处理委托方服务
     * @param message 消息内容
     * @return 响应消息
     */
    private String handleOutsysService(String message) {
        try {
            long start = System.nanoTime();
            log.info("报文发往委托方系统开始>>>");
            
            String response = sendoutsys(message);
            long end = System.nanoTime();
            
            log.info("报文发往委托方系统结束>>>");
            log.info("委托方系统返回报文耗时:[{}]毫秒", (end - start) / 1000L / 1000L);
            log.info("委托方系统返回报文长度:[{}]", response.getBytes("UTF-8").length);
            
            // 格式化响应消息（添加长度前缀）
            String formattedResponse = String.format("%06d", response.getBytes("UTF-8").length) + response;
            log.info("委托方系统返回报文:[{}]", response);
            
            return formattedResponse;
        } catch (Exception e) {
            log.error("处理委托方服务时发生错误", e);
            return "处理委托方服务时发生错误: " + e.getMessage();
        }
    }

    /**
     * 请求综合办公系统
     * @param reqmsg 请求消息
     * @param serviceno 服务号
     * @return 响应消息
     */
    private String sendZhbg(String reqmsg, String serviceno) throws Exception {
        String baseUrl = zhbgProperties.getZhbgPostUrl();
        String zhbgurl;
        
        if ("30001".equals(serviceno)) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求30001接口,传入json参数>>>>>>>>>>>>>>>>>>>>>>>>>>");
            zhbgurl = baseUrl + zhbgProperties.getUrl30001();
            log.info("请求URL: {}", zhbgurl);
            return sendHttpRequest(zhbgurl, reqmsg, "application/json");
        } else if ("20006".equals(serviceno)) {
            log.info(">>>>>>>>>>>>>>>>>>>>>>>>>>开始请求20006接口,传入xml参数>>>>>>>>>>>>>>>>>>>>>>>>>>");
            zhbgurl = baseUrl + zhbgProperties.getUrl20006();
            log.info("请求URL: {}", zhbgurl);
            // 根据URL协议选择合适的请求方法
            if (zhbgurl.startsWith("https://")) {
                return sendHttpsRequest(zhbgurl, reqmsg, "application/xml");
            } else {
                return sendHttpRequest(zhbgurl, reqmsg, "application/xml");
            }
        }
        
        throw new IllegalArgumentException("不支持的服务号: " + serviceno);
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String url, String params, String contentType) throws Exception {
        try {
            URL urlObj = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
            
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-type", contentType);
            conn.connect();
            
            try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), "utf-8")) {
                out.write(params);
                out.flush();
            }
            
            StringBuilder sb = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }
            
            String response = sb.toString();
            log.info("HTTP请求响应: {}", response);
            return response;
            
        } catch (HttpException e) {
            String errorMsg = "HTTP请求异常: " + e.getMessage();
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    /**
     * 发送HTTPS请求
     */
    private String sendHttpsRequest(String url, String params, String contentType) throws Exception {
        try {
            URL urlObj = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) urlObj.openConnection();
            
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-type", contentType);
            conn.connect();
            
            try (OutputStreamWriter out = new OutputStreamWriter(conn.getOutputStream(), "utf-8")) {
                out.write(params);
                out.flush();
            }
            
            StringBuilder sb = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
            }
            
            String response = sb.toString();
            log.info("HTTPS请求响应: {}", response);
            return response;
            
        } catch (HttpException e) {
            String errorMsg = "HTTPS请求异常: " + e.getMessage();
            log.error(errorMsg, e);
            return errorMsg;
        }
    }

    /**
     * 发送到委托方系统
     * 使用Spring注入的WebService客户端
     */
    private String sendoutsys(String reqmsg) throws Exception {
        try {
            log.info("调用委托方WebService，请求参数: {}", reqmsg);
            String response = webServiceClient.Execute("YCYH", reqmsg);
            log.info("委托方WebService响应: {}", response);
            return response;
        } catch (Exception e) {
            log.error("调用委托方WebService失败", e);
            throw new Exception("调用委托方WebService失败: " + e.getMessage(), e);
        }
    }

    /**
     * 判断是否是JSON结构
     */
    private boolean isJson(String value) {
        try {
            new JSONObject(value);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 判断是否是XML结构
     */
    private boolean isXML(String value) {
        try {
            DocumentHelper.parseText(value);
            return true;
        } catch (DocumentException e) {
            return false;
        }
    }
}
