# SSL兼容性说明

## 问题描述
在启用SSL后，遇到了Hutool HttpRequest类缺少SSL相关方法的编译错误：
- `setHttpsProtocol(String)` 方法不存在
- `disableDefaultSSLVerification()` 方法不存在

## 根本原因
当前项目使用的Hutool版本较旧，不支持这些SSL配置方法。这些方法在较新版本的Hutool中才被引入。

## 解决方案

### 1. 当前解决方案（已实施）
移除了不兼容的SSL配置方法，使用基础的HttpRequest功能：

```java
// 修改前（不兼容）
responseBody = HttpRequest.post(targetUrl)
        .header("Content-Type", "application/json")
        .body(requestBody)
        .setHttpsProtocol("TLSv1.2")           // 不存在的方法
        .disableDefaultSSLVerification()       // 不存在的方法
        .execute()
        .body();

// 修改后（兼容）
responseBody = HttpRequest.post(targetUrl)
        .header("Content-Type", "application/json")
        .body(requestBody)
        .execute()
        .body();
```

### 2. Hutool版本兼容性

#### 当前版本特点
- 基础HTTP请求功能正常
- 自动处理HTTPS连接
- 使用JVM默认的SSL配置

#### 如需高级SSL配置
可考虑以下方案：

**方案A: 升级Hutool版本**
```xml
<dependency>
    <groupId>cn.hutool</groupId>
    <artifactId>hutool-http</artifactId>
    <version>5.8.0</version> <!-- 或更新版本 -->
</dependency>
```

**方案B: 使用原生Java HttpURLConnection**
```java
private String sendHttpsRequest(String targetUrl, String requestBody, String method) {
    try {
        URL url = new URL(targetUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        
        // SSL配置
        if (conn instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConn = (HttpsURLConnection) conn;
            // 配置SSL上下文、信任管理器等
        }
        
        conn.setRequestMethod(method);
        conn.setRequestProperty("Content-Type", "application/json");
        conn.setDoOutput(true);
        
        // 发送请求体
        if ("POST".equals(method) && requestBody != null) {
            try (OutputStreamWriter writer = new OutputStreamWriter(conn.getOutputStream())) {
                writer.write(requestBody);
            }
        }
        
        // 读取响应
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(conn.getInputStream()))) {
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
            return response.toString();
        }
    } catch (Exception e) {
        log.error("HTTPS请求失败", e);
        return "请求失败: " + e.getMessage();
    }
}
```

**方案C: 使用Spring RestTemplate**
```java
@Autowired
private RestTemplate restTemplate;

private String sendRequest(String targetUrl, String requestBody, String method) {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    
    HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);
    
    if ("POST".equals(method)) {
        return restTemplate.postForObject(targetUrl, entity, String.class);
    } else if ("GET".equals(method)) {
        return restTemplate.getForObject(targetUrl, String.class);
    }
    
    return "不支持的请求方法";
}
```

## 当前状态

### ✅ 正常工作的功能
- HTTP转发服务启动
- 基础的HTTP/HTTPS请求转发
- SSL证书自动处理（使用JVM默认配置）
- 请求路径映射和转换

### ⚠️ 限制说明
- 无法自定义SSL协议版本
- 无法禁用SSL证书验证
- 依赖JVM的默认SSL配置

### 🔧 建议改进
1. **短期方案**: 继续使用当前配置，确保SSL证书有效
2. **中期方案**: 评估升级Hutool版本的影响
3. **长期方案**: 考虑迁移到Spring WebClient或其他现代HTTP客户端

## 测试验证

### 验证SSL功能
1. 启动应用，确认SSL配置生效
2. 测试HTTP转发服务到HTTPS Spring Boot应用的连接
3. 检查日志中的SSL握手信息

### 测试命令
```bash
# 测试HTTPS直接访问
curl -k https://127.0.0.1:8443/xmysfzjjg/xzp/test

# 测试HTTP转发服务
curl http://127.0.0.1:8445/xzp/test
```

## 相关文件
- `XzpUniversalHandler.java` - HTTP请求处理器
- `HttpForwardingService.java` - HTTP转发服务
- `application.yml` - SSL配置
- `SSL_CONFIGURATION_GUIDE.md` - SSL配置指南

## 注意事项
1. 当前解决方案适用于内网环境和受信任的证书
2. 生产环境建议使用有效的SSL证书
3. 如需更高级的SSL配置，建议升级相关依赖或使用替代方案